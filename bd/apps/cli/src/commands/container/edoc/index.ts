import { BaseCommand } from "@core/base";
import { Help } from "@oclif/core";

export default class EdocIndex extends BaseCommand<typeof EdocIndex> {
    static description = `Manage Edoc container:
    docker: Launch local docker-compose stack`;

    static examples = [
        `$ clara container edoc docker --env development`,
        `$ clara container edoc docker --env production`,
    ];

    public async process(): Promise<object> {
        // Display help for the edoc command
        const help = new Help(this.config);
        await help.showCommandHelp(this.config.findCommand("container:edoc"));

        return {
            message: "Edoc container help displayed",
        };
    }
}
