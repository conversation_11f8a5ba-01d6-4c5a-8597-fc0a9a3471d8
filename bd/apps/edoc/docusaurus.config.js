// @ts-check
// Note: type annotations allow type checking and IDEs autocompletion
import { themes as prismThemes } from "prism-react-renderer";

const redocusaurus = [
    "redocusaurus",
    {
        debug: <PERSON><PERSON><PERSON>(process.env.DEBUG || process.env.CI),
        specs: [
            {
                id: "console",
                spec: "api/console/api-spec.yml",
                route: "/api/console/",
            },
            {
                id: "nes",
                spec: "api/nes/api-spec.yml",
                route: "/api/nes/",
            },
        ],
        theme: {
            /**
             * Highlight color for docs
             */
            primaryColor: "#1890ff",
            /**
             * Options to pass to redoc
             * @see https://github.com/redocly/redoc#redoc-options-object
             */
            options: { disableSearch: true },
            /**
             * Options to pass to override RedocThemeObject
             * @see https://github.com/Redocly/redoc#redoc-theme-object
             */
            theme: {},
        },
    },
];
/** @type {import('@docusaurus/types').Config} */
const config = {
    title: "ClaraRx Wiki",
    tagline: "Better care coordination",
    url: "https://admin.envoylabs.net/",
    baseUrl: "/resource/",
    onBrokenLinks: "throw",
    onBrokenMarkdownLinks: "warn",
    favicon: "img/favicon.ico",

    organizationName: "ClaraRx",
    projectName: "@clara/edoc",

    i18n: {
        defaultLocale: "en",
        locales: ["en"],
    },

    presets: [
        [
            "classic",
            /** @type {import('@docusaurus/preset-classic').Options} */
            ({
                docs: {
                    sidebarPath: require.resolve("./sidebars.js"),
                    // Please change this to your repo.
                    // Remove this to remove the "edit this page" links.
                    // editUrl:
                    //   'https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/',
                },
                blog: {
                    showReadingTime: true,
                    // Please change this to your repo.
                    // Remove this to remove the "edit this page" links.
                    // editUrl:
                    //   'https://github.com/facebook/docusaurus/tree/main/packages/create-docusaurus/templates/shared/',
                },
                theme: {
                    customCss: require.resolve("./src/css/custom.css"),
                },
            }),
        ],
        redocusaurus,
    ],

    themeConfig:
        /** @type {import('@docusaurus/preset-classic').ThemeConfig} */
        ({
            // algolia: {
            //   appId: 'BXP31AOO75',
            //   apiKey: '********************************',
            //   indexName: 'docusaurus-2',
            // },
            navbar: {
                title: "ClaraRx Wiki",
                logo: {
                    alt: "Clara Logo",
                    src: "img/logo.svg",
                },
                items: [
                    {
                        type: "doc",
                        docId: "intro",
                        position: "left",
                        label: "How-To",
                    },
                    {
                        label: "API",
                        position: "left",
                        items: [
                            { to: "/api/console/", label: "Admin Console" },
                            { to: "/api/nes/", label: "NES" },
                        ],
                    },
                    {
                        href: "https://github.com/clararx",
                        label: "GitHub",
                        position: "right",
                    },
                    {
                        type: "search",
                        position: "right",
                    },
                ],
            },
            footer: {
                style: "dark",
                links: [
                    {
                        title: "Docs",
                        items: [
                            {
                                label: "Tutorial",
                                to: "/docs/intro",
                            },
                        ],
                    },
                    {
                        title: "More",
                        items: [
                            {
                                label: "GitHub",
                                href: "https://github.com/clararx",
                            },
                        ],
                    },
                ],
                copyright: `Copyright © ${new Date().getFullYear()} Clara, Inc.`,
            },
            prism: {
                theme: prismThemes.github,
                darkTheme: prismThemes.dracula,
            },
        }),
};

module.exports = config;
