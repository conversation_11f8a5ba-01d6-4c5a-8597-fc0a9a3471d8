services:
  edoc:
    volumes:
      - ../../../:/root/bd
    environment: # these override .env files
      - BASE_URL=https://wiki.local.clararx.com
    env_file:
      - .env # this gets created and deleted automatically by apps/cli `clara container` script
    build:
      context: ../../..
      dockerfile: packages/containers/edoc/Dockerfile
      target: ${NODE_ENV:-development}
      args:
        NODE_ENV: ${NODE_ENV:-development}
    labels:
      - wiki.orbstack.http-port=3080
    ports:
      - 3000:3000  # Only available in development mode
      - 3080:3080  # HTTP port for both dev and prod
      - 3443:3443  # HTTPS port for both dev and prod
