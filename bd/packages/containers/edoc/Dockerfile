# syntax=docker/dockerfile:1.7-labs

# STAGE: BASE
# Use <PERSON> instead of <PERSON> for better compatibility
FROM node:22-slim AS base

# Set environment (DO NOT combine into a single ENV command)
ARG NODE_ENV
ENV COREPACK_ENABLE_AUTO_PIN=0
ENV COREPACK_INTEGRITY_KEYS=0
ENV DEBIAN_FRONTEND=noninteractive
ENV NODE_ENV=$NODE_ENV
ENV SHELL=/bin/bash

ENV CLARA_HOME="/opt/clara"
ENV CLARA_BIN="$CLARA_HOME/apps/cli/src/index.ts"
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && mkdir -p $CLARA_HOME \
    && mkdir -p $PNPM_HOME \
    && corepack enable \
    && corepack prepare pnpm@latest --activate \
    && groupadd -r clara && useradd -r -g clara clara \
    && chown -R clara:clara $CLARA_HOME \
    && mkdir -p /var/log/clara && chown -R clara:clara /var/log/clara

# STAGE: DEPENDENCIES
FROM base AS dependencies

# Copy bd/ repo
WORKDIR $CLARA_HOME

# Run pnpm with just package info, without full source code
COPY ./.npmrc                     .
COPY ./tsconfig.json              .
COPY ./package.json               .
COPY ./pnpm-workspace.yaml        .
COPY apps/cli/src/index.ts        ./apps/cli/src/index.ts
COPY apps/cli/package.json        ./apps/cli/package.json
COPY apps/edoc/package.json       ./apps/edoc/package.json
COPY packages/config/package.json ./packages/config/package.json
COPY packages/dsl/package.json    ./packages/dsl/package.json
COPY packages/tslib/package.json  ./packages/tslib/package.json

# Install dependencies
RUN PNPM_INSTALL_ARGS="--loglevel=debug" \
    && if [ "$NODE_ENV" = "development" ] || [ "$NODE_ENV" = "testing" ]; then \
        PNPM_INSTALL_ARGS="$PNPM_INSTALL_ARGS --unsafe-perm"; \
    fi \
    && pnpm i -r --cache-dir .pnpm-cache $PNPM_INSTALL_ARGS \
    && pnpm rb -r $PNPM_INSTALL_ARGS

# STAGE: DEVELOPMENT
FROM dependencies AS development

# Copy full source code
COPY --chown=clara:clara . .

# Build edoc
RUN pnpm --filter @clara/edoc build \
    && rm -rf .pnpm-cache

# Install PM2 globally
RUN pnpm add -g pm2

# Copy PM2 configuration
COPY packages/containers/edoc/config/pm2/ecosystem.config.cjs ./config/pm2/edoc.config.cjs

# Copy Caddy configuration
COPY packages/containers/edoc/config/caddy/ ./config/caddy/

# Expose ports
EXPOSE 3000 3080 3443

# Switch to clara user
USER clara

# Start PM2
CMD ["pm2-runtime", "start", "./config/pm2/edoc.config.cjs"]

# STAGE: PRODUCTION
FROM development AS production

# Production optimizations
ENV NODE_ENV=production

# Remove dev dependencies and rebuild
RUN pnpm i -r --cache-dir .pnpm-cache --prod \
    && rm -rf .pnpm-cache

# Start PM2 in production mode
CMD ["pm2-runtime", "start", "./config/pm2/edoc.config.cjs"]
