# syntax=docker/dockerfile:1.7-labs

# STAGE: BASE
# Use <PERSON> instead of <PERSON> for better compatibility
FROM node:22-slim AS base

# Set environment (DO NOT combine into a single ENV command)
ARG NODE_ENV
ENV COREPACK_ENABLE_AUTO_PIN=0
ENV COREPACK_INTEGRITY_KEYS=0
ENV DEBIAN_FRONTEND=noninteractive
ENV NODE_ENV=$NODE_ENV
ENV SHELL=/bin/bash

ENV CLARA_HOME="/opt/clara"
ENV CLARA_BIN="$CLARA_HOME/apps/cli/src/index.ts"
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && mkdir -p $CLARA_HOME \
    && mkdir -p $PNPM_HOME \
    && corepack enable \
    && corepack prepare pnpm@latest --activate \
    && groupadd -r clara && useradd -r -g clara clara \
    && chown -R clara:clara $CLARA_HOME \
    && mkdir -p /var/log/clara && chown -R clara:clara /var/log/clara

# STAGE: DEPENDENCIES
FROM base AS dependencies

# Copy bd/ repo
WORKDIR $CLARA_HOME

# Run pnpm with just package info, without full source code
COPY ./.npmrc                     .
COPY ./tsconfig.json              .
COPY ./package.json               .
COPY ./pnpm-workspace.yaml        .
COPY apps/cli/src/index.ts        ./apps/cli/src/index.ts
COPY apps/cli/package.json        ./apps/cli/package.json
COPY apps/edoc/package.json       ./apps/edoc/package.json
COPY packages/config/package.json ./packages/config/package.json
COPY packages/dsl/package.json    ./packages/dsl/package.json
COPY packages/tslib/package.json  ./packages/tslib/package.json

# Install dependencies
RUN PNPM_INSTALL_ARGS="--loglevel=debug" \
    && if [ "$NODE_ENV" = "development" ] || [ "$NODE_ENV" = "testing" ]; then \
        PNPM_INSTALL_ARGS="$PNPM_INSTALL_ARGS --unsafe-perm"; \
    fi \
    && pnpm i -r --cache-dir .pnpm-cache $PNPM_INSTALL_ARGS \
    && pnpm rb -r $PNPM_INSTALL_ARGS

# STAGE: DEVELOPMENT
FROM dependencies AS development

# Copy full source code
COPY --chown=clara:clara . .

# Install PM2 globally
RUN pnpm add -g pm2

# Copy PM2 configuration
COPY bd/packages/containers/edoc/config/pm2/ecosystem.config.cjs ./config/pm2/edoc.config.cjs

# Copy Caddy configuration
COPY bd/packages/containers/edoc/config/caddy/ ./config/caddy/

# Expose ports
EXPOSE 3000 3080 3443

# Switch to clara user
USER clara

# Start PM2
CMD ["pm2-runtime", "start", "./config/pm2/edoc.config.cjs"]

# STAGE: BUILD
FROM dependencies AS build

# Copy full source code
COPY --chown=clara:clara bd/ .

# Build edoc static files
RUN pnpm --filter @clara/edoc build \
    && rm -rf .pnpm-cache

# STAGE: PRODUCTION
FROM caddy:2-alpine AS production

# Install ca-certificates for HTTPS
RUN apk --no-cache add ca-certificates

# Create clara user and directories
RUN addgroup -g 1000 clara \
    && adduser -u 1000 -G clara -s /bin/sh -D clara \
    && mkdir -p /var/log/clara \
    && chown -R clara:clara /var/log/clara

# Copy built static files from build stage
COPY --from=build --chown=clara:clara /opt/clara/apps/edoc/dist /opt/clara/apps/edoc/dist

# Copy Caddy configuration
COPY --chown=clara:clara bd/packages/containers/edoc/config/caddy/ /opt/clara/config/caddy/

# Copy certificates directory (create empty if doesn't exist)
COPY --chown=clara:clara bd/packages/containers/edoc/config/certs/ /opt/clara/config/certs/

# Expose ports
EXPOSE 3080 3443

# Switch to clara user
USER clara

# Start Caddy with production configuration
CMD ["caddy", "run", "--adapter=caddyfile", "--config", "/opt/clara/config/caddy/docker.prod"]
